# Vue3 Website 项目

## 项目结构说明

```
vue3-website/
├── public/                   # 静态资源
├── src/
│   ├── assets/              # 资源文件
│   │   ├── images/          # 图片
│   │   ├── styles/          # 样式文件
│   │   └── fonts/           # 字体文件
│   ├── components/          # 公共组件
│   │   ├── common/          # 通用组件
│   │   ├── layout/          # 布局组件
│   │   └── business/        # 业务组件
│   ├── views/               # 页面组件
│   │   ├── Home/            # 首页
│   │   ├── About/           # 关于我们
│   │   ├── Solutions/       # 解决方案
│   │   ├── Products/        # 产品服务
│   │   └── News/            # 行业洞察
│   ├── router/              # 路由配置
│   ├── stores/              # 状态管理
│   ├── composables/         # 组合式函数
│   ├── utils/               # 工具函数
│   ├── types/               # TypeScript类型定义
│   └── locales/             # 国际化文件
├── tests/                   # 测试文件
└── docs/                    # 文档
```

## 目录说明

- **public/**: 存放静态资源，如 favicon.ico、index.html 等
- **src/assets/**: 存放项目资源文件
  - **images/**: 图片资源
  - **styles/**: CSS/SCSS 样式文件
  - **fonts/**: 字体文件
- **src/components/**: 可复用的Vue组件
  - **common/**: 通用组件（按钮、输入框等）
  - **layout/**: 布局组件（头部、底部、侧边栏等）
  - **business/**: 业务相关组件
- **src/views/**: 页面级组件
  - **Home/**: 首页相关组件
  - **About/**: 关于我们页面
  - **Solutions/**: 解决方案页面
  - **Products/**: 产品服务页面
  - **News/**: 行业洞察页面
- **src/router/**: Vue Router 路由配置
- **src/stores/**: Pinia 状态管理
- **src/composables/**: Vue3 组合式函数
- **src/utils/**: 工具函数和帮助方法
- **src/types/**: TypeScript 类型定义
- **src/locales/**: 国际化配置文件
- **tests/**: 单元测试和集成测试
- **docs/**: 项目文档

## 开发规范

- 使用 Vue3 + TypeScript + Vite
- 采用 Composition API
- 使用 Pinia 进行状态管理
- 支持国际化 (i18n)
- 遵循组件化开发原则
